{"name": "backend", "version": "1.0.0", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "nodemon src/index.ts", "start": "node dist/index.js", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "db:studio": "prisma studio", "db:reset": "prisma migrate reset", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@prisma/client": "^6.11.1", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "prisma": "^6.11.1"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/node": "^24.0.13", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}