# Atorpos POS Sistemi

Basit restoran POS sistemi

## Stack
- Frontend: Electron + React + TypeScript + Tailwind
- Backend: Express + TypeScript + Prisma + PostgreSQL

## Klasör Yapısı
- `backend/` - Express API server
- `frontend/` - Electron + React uygulaması

## Kurulum

### Backend
```bash
cd backend
npm install
npm run dev
```

### Frontend
```bash
cd frontend
npm install
npm run dev
```
# 1. Backend klasörüne git
cd backend

# 2. İlk migration oluştur
npx prisma migrate dev --name init

# 3. Prisma client generate et
npx prisma generate

# 4. (Opsiyonel) Prisma Studio ile kontrol et
npx prisma studio

# 5. Database Reset (Geliştirme)
cd backend
npx prisma migrate reset

# 6. Schema Değişikliği Sonrası
cd backend
npx prisma migrate dev --name "migration_adi"
npx prisma generate

# ⚠️ Önemli Notlar

Development: migrate dev kullan
Production: migrate deploy kullan
Schema değişikliği sonrası: Her zaman prisma generate çalıştır
Database URL: .env dosyasında DATABASE_URL ayarlandığından emin ol